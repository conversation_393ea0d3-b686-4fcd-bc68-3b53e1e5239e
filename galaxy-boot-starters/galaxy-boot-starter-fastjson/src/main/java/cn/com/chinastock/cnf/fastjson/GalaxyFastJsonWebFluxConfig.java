package cn.com.chinastock.cnf.fastjson;

import com.alibaba.fastjson2.support.config.FastJsonConfig;
import com.alibaba.fastjson2.support.spring6.http.codec.Fastjson2Decoder;
import com.alibaba.fastjson2.support.spring6.http.codec.Fastjson2Encoder;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.lang.NonNull;
import org.springframework.web.reactive.config.WebFluxConfigurer;

import static cn.com.chinastock.cnf.fastjson.FastJsonFeatureUtil.collectReaderFromProperties;
import static cn.com.chinastock.cnf.fastjson.FastJsonFeatureUtil.collectWriterFromProperties;

/**
 * Galaxy FastJSON WebFlux 自动配置类
 * <p>
 * 为 Spring WebFlux 应用提供 FastJSON 支持，替换默认的 Jackson。
 * 该配置类只在响应式 Web 应用环境中生效，避免与 MVC 配置冲突。
 * </p>
 * <p>
 * 主要功能：
 * <ul>
 *   <li>创建 WebFlux 专用的 FastJSON 配置</li>
 *   <li>提供 Fastjson2Encoder 和 Fastjson2Decoder Bean</li>
 *   <li>通过 WebFluxConfigurer 注册自定义编解码器</li>
 *   <li>支持所有 FastJsonProperties 配置项</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Boot Team
 * @see GalaxyFastJsonConfig MVC 版本的配置类
 * @see FastJsonProperties FastJSON 配置属性
 */
@Configuration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.REACTIVE)
@ConditionalOnClass({
    org.springframework.web.reactive.config.WebFluxConfigurer.class,
    com.alibaba.fastjson2.support.spring6.http.codec.Fastjson2Decoder.class,
    com.alibaba.fastjson2.support.spring6.http.codec.Fastjson2Encoder.class
})
@EnableConfigurationProperties(FastJsonProperties.class)
public class GalaxyFastJsonWebFluxConfig implements WebFluxConfigurer, ApplicationContextAware {

    @Autowired
    private FastJsonProperties fastJsonProperties;

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * 创建 WebFlux 专用的 FastJSON 配置
     * <p>
     * 复用 MVC 配置的逻辑，确保配置的一致性。
     * 该配置会被 Fastjson2Encoder 和 Fastjson2Decoder 使用。
     * </p>
     *
     * @return WebFlux 专用的 FastJSON 配置
     */
    @Bean
    public FastJsonConfig webfluxFastJsonConfig() {
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setCharset(java.nio.charset.StandardCharsets.UTF_8);
        fastJsonConfig.setWriterFeatures(collectWriterFromProperties(fastJsonProperties));
        fastJsonConfig.setReaderFeatures(collectReaderFromProperties(fastJsonProperties));

        return fastJsonConfig;
    }

    /**
     * 创建 FastJSON WebFlux 编码器
     * <p>
     * 使用 ObjectMapper 和 FastJsonConfig 配置编码器，确保与配置属性一致。
     * 支持 application/json 和 application/*+json 媒体类型。
     * </p>
     *
     * @param webfluxObjectMapper WebFlux专用的ObjectMapper
     * @param webfluxFastJsonConfig WebFlux专用的FastJSON配置
     * @return FastJSON WebFlux 编码器
     */
    @Bean
    public Fastjson2Encoder fastjson2Encoder(ObjectMapper webfluxObjectMapper, FastJsonConfig webfluxFastJsonConfig) {
        return new Fastjson2Encoder(webfluxObjectMapper, webfluxFastJsonConfig);
    }

    /**
     * 创建 FastJSON WebFlux 解码器
     * <p>
     * 使用 ObjectMapper 和 FastJsonConfig 配置解码器，确保与配置属性一致。
     * 支持 application/json 和 application/*+json 媒体类型。
     * </p>
     *
     * @param webfluxObjectMapper WebFlux专用的ObjectMapper
     * @param webfluxFastJsonConfig WebFlux专用的FastJSON配置
     * @return FastJSON WebFlux 解码器
     */
    @Bean
    public Fastjson2Decoder fastjson2Decoder(ObjectMapper webfluxObjectMapper, FastJsonConfig webfluxFastJsonConfig) {
        return new Fastjson2Decoder(webfluxObjectMapper, webfluxFastJsonConfig);
    }

    /**
     * 配置 HTTP 消息编解码器
     * <p>
     * 通过 ApplicationContext 获取编解码器 Bean，避免循环依赖问题。
     * 关键改进：
     * <ul>
     *   <li>使用 ApplicationContext.getBean() 获取编解码器</li>
     *   <li>避免字段注入导致的循环依赖</li>
     *   <li>正确替换默认的 Jackson 编解码器</li>
     * </ul>
     * </p>
     *
     * @param configurer 服务器编解码器配置器
     */
    @Override
    public void configureHttpMessageCodecs(@NonNull ServerCodecConfigurer configurer) {
        Fastjson2Encoder encoder = applicationContext.getBean(Fastjson2Encoder.class);
        Fastjson2Decoder decoder = applicationContext.getBean(Fastjson2Decoder.class);

        configurer.defaultCodecs().jackson2JsonEncoder(encoder);
        configurer.defaultCodecs().jackson2JsonDecoder(decoder);
    }
}
