package cn.com.chinastock.cnf.fastjson;

import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.support.config.FastJsonConfig;
import com.alibaba.fastjson2.support.spring6.http.converter.FastJsonHttpMessageConverter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Configuration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@EnableConfigurationProperties(FastJsonProperties.class)
public class GalaxyFastJsonConfig implements WebMvcConfigurer {
    private final FastJsonProperties fastJsonProperties;

    public GalaxyFastJsonConfig(FastJsonProperties fastJsonProperties) {
        this.fastJsonProperties = fastJsonProperties;
    }

    /**
     * 配置 FastJson 作为 Spring MVC 的 HTTP 消息转换器
     * <p>
     * 消息转换器初始化流程：
     * <p>
     * {@link  org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport#getMessageConverters }
     * 消息转换器的初始化逻辑如下：
     * <ol>
     * <li>如果用户覆盖了 configureMessageConverters，则仅使用用户配置的转换器。</li>
     * <li>如果没有覆盖 configureMessageConverters，默认会调用 {@link org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport#addDefaultHttpMessageConverters }，加载 Spring 内置的默认转换器。</li>
     * <li>在上述基础上，无论用户是否覆盖 configureMessageConverters，都会调用 extendMessageConverters 方法，允许对已存在的转换器列表进行扩展。</li>
     * </ol>
     * <p>
     * 扩展转换器的整合流程：
     * {@link org.springframework.web.servlet.config.annotation.WebMvcConfigurerComposite#extendMessageConverters }
     * WebMvcConfigurerComposite 会收集所有实现 WebMvcConfigurer 接口的配置类（例如用户自定义配置类和其他自动配置类），
     * 并统一调用它们的 extendMessageConverters 方法来扩展转换器列表。
     * <p>
     * HttpMessageConverter 配置示例（Spring Boot Data）：
     * <p>
     * {@link org.springframework.data.web.config.SpringDataWebConfiguration#extendMessageConverters }
     * SpringDataWebConfiguration 是 Spring Data 的一个自动配置类， 它会在消息转换器列表中添加 ProjectingJackson2HttpMessageConverter，
     * 前提是应用上下文中存在 Jackson 的 ObjectMapper 实例。
     * <p>
     * 注意事项：
     * - 如果使用 FastJsonHttpMessageConverter，需要手动注册并调整其优先级，以避免与默认的 MappingJackson2HttpMessageConverter 冲突。
     * - 在 Spring Boot 项目中，通常建议通过实现 WebMvcConfigurer 来配置自定义消息转换器，而不是直接修改 WebMvcConfigurationSupport。
     * {@link org.springframework.data.web.ProjectingJackson2HttpMessageConverter }
     *
     * @param fastJsonConfig FastJsonConfig配置
     * @return FastJsonHttpMessageConverter 配置完成的消息转换器
     */
    @Bean
    public FastJsonHttpMessageConverter fastJsonHttpMessageConverter(FastJsonConfig fastJsonConfig) {
        FastJsonHttpMessageConverter fastConverter = new FastJsonHttpMessageConverter();

        // 处理中文乱码问题
        List<MediaType> fastMediaTypes = new ArrayList<>();
        fastMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        fastMediaTypes.add(MediaType.APPLICATION_JSON);

        /// 支持金蝶 text/json 类型
        MediaType mediaTypeJson = MediaType.valueOf("text/json;charset=UTF-8");
        fastMediaTypes.add(mediaTypeJson);

        fastConverter.setSupportedMediaTypes(fastMediaTypes);
        fastConverter.setFastJsonConfig(fastJsonConfig);
        return fastConverter;
    }

    @Bean
    @ConditionalOnMissingBean
    public FastJsonConfig defaultFastJsonConfig() {
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setCharset(StandardCharsets.UTF_8);
        fastJsonConfig.setWriterFeatures(FastJsonFeatureUtil.collectWriterFromProperties(fastJsonProperties).toArray(new JSONWriter.Feature[0]));
        fastJsonConfig.setReaderFeatures(FastJsonFeatureUtil.collectReaderFromProperties(fastJsonProperties).toArray(new JSONReader.Feature[0]));
        return fastJsonConfig;
    }
}
